import { Router } from 'express';
import { PrismaClient, Project } from '@prisma/client';

const router = Router();
const prisma = new PrismaClient();

// Simple role helper using header. Default to internal if missing.
import type { Request } from 'express';
const isCustomer = (req: Request) => String(req.headers['x-user-role'] || '').toLowerCase() === 'customer';

// Status helpers (mirror frontend typings)
const DESIGNER_STATUSES = [
  'designer_pending_assign', 'checklist', '3d', '2d', 'furniture_list', 'complete', 'designer_lost_deal'
] as const;
const SUPERVISOR_STATUSES = [
  'supervisor_pending_assign', 'inprogress', 'completed', 'supervisor_lost_deal'
] as const;

type Role = 'sales' | 'designer' | 'supervisor';

export function classifyRole(p: Project): Role {
  // Prefer structure over status text to avoid ambiguity with `completed`
  if (!p.parentTaskId) return 'sales';
  if (DESIGNER_STATUSES.includes(p.status as (typeof DESIGNER_STATUSES)[number])) return 'designer';
  if (SUPERVISOR_STATUSES.includes(p.status as (typeof SUPERVISOR_STATUSES)[number])) return 'supervisor';
  // Fallback: try to infer by status family
  if (p.status === 'complete') return 'designer';
  return 'sales';
}

export function pickLatest<T extends { updatedAt: Date }>(items: T[]): T | undefined {
  return items.slice().sort((a, b) => b.updatedAt.getTime() - a.updatedAt.getTime())[0];
}

router.get('/', async (req, res) => {
  // Query params: pagination, sort, filters
  const page = Math.max(parseInt(String(req.query.page || '1')), 1);
  const pageSize = Math.min(Math.max(parseInt(String(req.query.pageSize || '20')), 1), 100);
  const sort = String(req.query.sort || 'lastUpdateAt'); // only supports lastUpdateAt for now
  const sortDir = String(req.query.sortDir || 'desc').toLowerCase() === 'asc' ? 'asc' : 'desc';
  const roleFilter = (String(req.query.role || '') as 'sales'|'designer'|'supervisor'|'' );
  const statusFilter = String(req.query.status || '');
  const assignedToFilter = String(req.query.assignedTo || '');
  const dateFrom = req.query.dateFrom ? new Date(String(req.query.dateFrom)) : null;
  const dateTo = req.query.dateTo ? new Date(String(req.query.dateTo)) : null;

  // Fetch projects; basic ordering for determinism
  const projects = await prisma.project.findMany({ orderBy: { updatedAt: 'desc' } });

  const byCase: Record<string, Project[]> = {};
  for (const p of projects) {
    const key = p.caseId || p.id;
    if (!byCase[key]) byCase[key] = [];
    byCase[key].push(p);
  }

  let cases = Object.entries(byCase).map(([caseId, items]) => {
    const sales = items.filter(i => classifyRole(i) === 'sales');
    const designer = items.filter(i => classifyRole(i) === 'designer');
    const supervisor = items.filter(i => classifyRole(i) === 'supervisor');

    // Prefer root sales record for shared fields
    const root = sales[0] ?? items[0];

    const roles = {
      sales: pickLatest(sales) || null,
      designer: pickLatest(designer) || null,
      supervisor: pickLatest(supervisor) || null,
    };

    const lastUpdateAt = items.reduce((acc, i) => Math.max(acc, i.updatedAt.getTime()), 0);

    const base = {
      caseId,
      title: root?.title ?? '',
      client: root?.client ?? '',
      salesAmount: root?.salesAmount ?? 0,
      lastUpdateAt: new Date(lastUpdateAt).toISOString(),
      roles: {
        sales: roles.sales && {
          id: roles.sales.id,
          status: roles.sales.status,
          assignedTo: roles.sales.assignedTo,
          assignedToName: roles.sales.assignedTo || null,
          createdAt: roles.sales.createdAt,
          updatedAt: roles.sales.updatedAt,
        },
        designer: roles.designer && {
          id: roles.designer.id,
          status: roles.designer.status,
          assignedTo: roles.designer.assignedTo,
          assignedToName: roles.designer.assignedTo || null,
          createdAt: roles.designer.createdAt,
          updatedAt: roles.designer.updatedAt,
        },
        supervisor: roles.supervisor && {
          id: roles.supervisor.id,
          status: roles.supervisor.status,
          assignedTo: roles.supervisor.assignedTo,
          assignedToName: roles.supervisor.assignedTo || null,
          createdAt: roles.supervisor.createdAt,
          updatedAt: roles.supervisor.updatedAt,
          supervisorSelectedPhases: roles.supervisor.supervisorSelectedPhases,
          supervisorPhaseDates: roles.supervisor.supervisorPhaseDates,
        },
      },
    };

    return base;
  });

  // Filters (in-memory)
  if (roleFilter) {
    cases = cases.filter(c => {
      const item = (c.roles as Record<'sales'|'designer'|'supervisor', { status: string; assignedTo: string | null } | null>)[roleFilter];
      if (!item) return false;
      if (statusFilter && item.status !== statusFilter) return false;
      if (assignedToFilter && item.assignedTo !== assignedToFilter) return false;
      return true;
    });
  } else if (statusFilter || assignedToFilter) {
    cases = cases.filter(c => {
      const roles = c.roles as Record<'sales'|'designer'|'supervisor', { status?: string; assignedTo?: string | null } | null>;
      const anyMatch = (['sales','designer','supervisor'] as const).some(r => {
        const item = roles[r];
        if (!item) return false;
        const statusOk = statusFilter ? item.status === statusFilter : true;
        const assignedOk = assignedToFilter ? item.assignedTo === assignedToFilter : true;
        return statusOk && assignedOk;
      });
      return anyMatch;
    });
  }
  if (dateFrom || dateTo) {
    cases = cases.filter(c => {
      const ts = new Date(c.lastUpdateAt).getTime();
      if (dateFrom && ts < dateFrom.getTime()) return false;
      if (dateTo && ts > dateTo.getTime()) return false;
      return true;
    });
  }

  // Sorting
  if (sort === 'lastUpdateAt') {
    cases.sort((a, b) => sortDir === 'asc' ?
      new Date(a.lastUpdateAt).getTime() - new Date(b.lastUpdateAt).getTime() :
      new Date(b.lastUpdateAt).getTime() - new Date(a.lastUpdateAt).getTime());
  }

  // Pagination (with total count)
  const total = cases.length;
  const start = (page - 1) * pageSize;
  const end = start + pageSize;
  const paged = cases.slice(start, end);
  const totalPages = Math.ceil(total / pageSize);

  // Mask for customers (hide sensitive fields)
  if (isCustomer(req)) {
    const masked = paged.map(c => ({
      ...c,
      salesAmount: 0,
      roles: {
        sales: c.roles.sales && { id: c.roles.sales.id, status: c.roles.sales.status, createdAt: c.roles.sales.createdAt, updatedAt: c.roles.sales.updatedAt },
        designer: c.roles.designer && { id: c.roles.designer.id, status: c.roles.designer.status, createdAt: c.roles.designer.createdAt, updatedAt: c.roles.designer.updatedAt },
        supervisor: c.roles.supervisor && { id: c.roles.supervisor.id, status: c.roles.supervisor.status, createdAt: c.roles.supervisor.createdAt, updatedAt: c.roles.supervisor.updatedAt },
      }
    }));
    return res.json({ items: masked, page, pageSize, total, totalPages });
  }

  res.json({ items: paged, page, pageSize, total, totalPages });
});

router.get('/:caseId', async (req, res) => {
  const { caseId } = req.params;
  const items = await prisma.project.findMany({ where: { caseId } });
  if (!items || items.length === 0) return res.status(404).json({ error: 'Not found' });

  const sales = items.filter(i => classifyRole(i) === 'sales');
  const designer = items.filter(i => classifyRole(i) === 'designer');
  const supervisor = items.filter(i => classifyRole(i) === 'supervisor');
  const root = sales[0] ?? items[0];

  const detail = {
    caseId,
    title: root?.title ?? '',
    client: root?.client ?? '',
    salesAmount: root?.salesAmount ?? 0,
    roles: {
      sales: (() => { const r = pickLatest(sales); return r && { ...r, assignedToName: r.assignedTo || null }; })() || null,
      designer: (() => { const r = pickLatest(designer); return r && { ...r, assignedToName: r.assignedTo || null }; })() || null,
      supervisor: (() => { const r = pickLatest(supervisor); return r && { ...r, assignedToName: r.assignedTo || null, supervisorSelectedPhases: r.supervisorSelectedPhases, supervisorPhaseDates: r.supervisorPhaseDates }; })() || null,
    }
  };

  if (isCustomer(req)) {
    return res.json({
      ...detail,
      salesAmount: 0,
      roles: {
        sales: detail.roles.sales && { id: detail.roles.sales.id, status: detail.roles.sales.status, createdAt: detail.roles.sales.createdAt, updatedAt: detail.roles.sales.updatedAt },
        designer: detail.roles.designer && { id: detail.roles.designer.id, status: detail.roles.designer.status, createdAt: detail.roles.designer.createdAt, updatedAt: detail.roles.designer.updatedAt },
        supervisor: detail.roles.supervisor && { id: detail.roles.supervisor.id, status: detail.roles.supervisor.status, createdAt: detail.roles.supervisor.createdAt, updatedAt: detail.roles.supervisor.updatedAt },
      }
    });
  }

  res.json(detail);
});

router.get('/:caseId/history', async (req, res) => {
  const { caseId } = req.params;
  const items = await prisma.project.findMany({ where: { caseId }, orderBy: { createdAt: 'asc' } });
  if (!items || items.length === 0) return res.status(404).json({ error: 'Not found' });

  type Event = {
    id: string;
    caseId: string;
    role: Role;
    type: string;
    timestamp: string;
    user?: { id: string | null; name?: string | null } | null;
    sourceId: string;
    payload?: { kind?: string; phase?: string; status?: string; selected?: string[]; from?: string; to?: string; fromUserId?: string; toUserId?: string; changes?: any[] };
  };

  // First, try to get events from TaskEvent table
  let events: Event[] = [];

  try {
    const taskEvents = await prisma.$queryRaw<Array<{
      id: string;
      caseId: string;
      taskId: string;
      role: string;
      type: string;
      occurredAt: Date;
      actorUserId: string | null;
      source: string;
      payload: any;
    }>>`
      SELECT * FROM "TaskEvent"
      WHERE "caseId" = ${caseId}::uuid
      ORDER BY "occurredAt" ASC
    `;

    // Get unique user IDs for lookup
    const userIds = [...new Set(taskEvents.map(e => e.actorUserId).filter(Boolean))];
    const users = userIds.length > 0 ? await prisma.$queryRaw<Array<{
      id: string;
      name: string;
    }>>`
      SELECT id, name FROM "User" WHERE id = ANY(${userIds}::text[])
    ` : [];

    const userMap = new Map(users.map(u => [u.id, u.name]));

    events = taskEvents.map(e => ({
      id: e.id,
      caseId: e.caseId,
      role: e.role as Role,
      type: e.type,
      timestamp: e.occurredAt.toISOString(),
      user: e.actorUserId ? { id: e.actorUserId, name: userMap.get(e.actorUserId) || null } : null,
      sourceId: e.taskId,
      payload: e.payload
    }));
  } catch (error) {
    // Fallback to legacy event generation if TaskEvent table doesn't exist or query fails
    console.warn('TaskEvent table not available, falling back to legacy events:', error);

    for (const p of items) {
      const role = classifyRole(p);
      // Task created event
      events.push({
        id: `${p.id}:created`,
        caseId,
        role,
        type: 'task_created',
        timestamp: p.createdAt.toISOString(),
        user: null,
        sourceId: p.id,
        payload: { status: p.status }
      });

      if (role === 'designer') {
        // Revisions from arrays
        (p.revisions3d || []).forEach((ts, idx) => {
          events.push({ id: `${p.id}:rev3d:${idx}`, caseId, role: 'designer', type: 'designer_revision', timestamp: ts, user: null, sourceId: p.id, payload: { kind: '3d' } });
        });
        (p.revisions2d || []).forEach((ts, idx) => {
          events.push({ id: `${p.id}:rev2d:${idx}`, caseId, role: 'designer', type: 'designer_revision', timestamp: ts, user: null, sourceId: p.id, payload: { kind: '2d' } });
        });
      }

      if (role === 'supervisor') {
        // Selection snapshot (best-effort): when supervisor record exists
        if ((p.supervisorSelectedPhases || []).length > 0) {
          events.push({
            id: `${p.id}:supervisor_assigned`,
            caseId,
            role: 'supervisor',
            type: 'supervisor_task_assigned',
            timestamp: p.createdAt.toISOString(), // best-effort; future: add explicit timestamp
            user: null,
            sourceId: p.id,
            payload: { selected: p.supervisorSelectedPhases }
          });
        }
        // Completion events from phase dates
        const dates = (p.supervisorPhaseDates as unknown as Record<string, string>) || {};
        Object.entries(dates).forEach(([phase, ts]) => {
          if (ts) {
            events.push({ id: `${p.id}:phase:${phase}`, caseId, role: 'supervisor', type: 'supervisor_task_completed', timestamp: ts, user: null, sourceId: p.id, payload: { phase } });
          }
        });
      }
    }
  }

  // Sort by timestamp asc
  events.sort((a, b) => new Date(a.timestamp).getTime() - new Date(b.timestamp).getTime());

  if (isCustomer(req)) {
    const maskedEvents = events.map(e => ({ id: e.id, caseId: e.caseId, role: e.role, type: e.type, timestamp: e.timestamp, user: null, sourceId: e.sourceId }));
    return res.json({ caseId, events: maskedEvents });
  }

  res.json({ caseId, events });
});

export default router;

