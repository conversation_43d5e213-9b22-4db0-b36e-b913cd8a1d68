import { Badge } from "@/components/ui/badge";
import { statusLabel, statusToTone, toneToClasses } from "@/lib/status";

interface StatusBadgeProps {
  status: string;
  className?: string;
}

export function StatusBadge({ status, className }: StatusBadgeProps) {
  const tone = statusToTone(status);
  const classes = toneToClasses(tone);
  return (
    <Badge className={`${classes} ${className || ""}`.trim()}>{statusLabel(status)}</Badge>
  );
}

