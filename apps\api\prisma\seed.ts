import { PrismaClient } from '@prisma/client';
import { randomUUID } from 'crypto';

const prisma = new PrismaClient();

async function main() {
  // Clear existing data
  await prisma.project.deleteMany();

  // Create sample projects (let Prisma generate UUIDs)
  const projects = [
    {
      caseId: randomUUID(),
      title: 'Modern Office Renovation',
      client: 'Tech Corp Inc.',
      status: 'quotation',
      createdAt: new Date('2024-01-15'),
      updatedAt: new Date('2024-01-15'),
      assignedTo: '1', // <PERSON> ID
      salesAmount: 85000
    },
    {
      caseId: randomUUID(),
      title: 'Residential Kitchen Remodel',
      client: 'The Smith Family',
      status: 'won_deal',
      createdAt: new Date('2024-01-10'),
      updatedAt: new Date('2024-01-10'),
      assignedTo: '1', // <PERSON> ID
      salesAmount: 75000,
      salesSubStatus: '5%',
      remarks: 'Client requested premium finishes and extended timeline for quality work.'
    },
    {
      caseId: randomUUID(),
      title: 'Retail Store Buildout',
      client: 'Fashion Boutique LLC',
      status: '3d',
      createdAt: new Date('2024-01-08'),
      updatedAt: new Date('2024-01-08'),
      assignedTo: '2', // <PERSON> Chen ID
      salesAmount: 120000,
      dueDate: new Date('2024-01-20'),
      expiredDate: new Date('2024-01-18'),
      remarks: 'Focus on modern aesthetic with sustainable materials. Client very particular about lighting design. Additional requirements include: 1) Energy-efficient LED lighting throughout the space, 2) Sustainable flooring materials (bamboo or reclaimed wood preferred), 3) Modern minimalist design with clean lines, 4) Neutral color palette with accent walls, 5) Open floor plan to maximize natural light, 6) Integration of smart home technology, 7) High-quality finishes that will stand the test of time, 8) Consideration for future expansion possibilities, 9) Compliance with all local building codes and accessibility requirements, 10) Timeline is flexible but quality is paramount - client willing to wait for the right materials and craftsmanship.',
      revisions3d: ['2024-01-09T10:00:00Z', '2024-01-11T14:30:00Z', '2024-01-13T09:15:00Z', '2024-01-15T16:45:00Z']
    },
    {
      caseId: randomUUID(),
      title: 'Conference Room Upgrade',
      client: 'Law Firm Partners',
      status: 'plaster_ceiling',
      createdAt: new Date('2024-01-05'),
      updatedAt: new Date('2024-01-05'),
      assignedTo: '3', // David Rodriguez ID
      salesAmount: 65000
    },
    {
      caseId: randomUUID(),
      title: 'Warehouse Renovation',
      client: 'Storage Solutions Inc',
      status: 'carpentry_install',
      createdAt: new Date('2024-01-12'),
      updatedAt: new Date('2024-01-12'),
      assignedTo: '4', // Lisa Wang ID (manager)
      salesAmount: 95000
    },
    {
      caseId: randomUUID(),
      title: 'Corporate Office Design',
      client: 'Business Corp Ltd',
      status: 'designer_pending_assign',
      createdAt: new Date('2024-01-14'),
      updatedAt: new Date('2024-01-14'),
      assignedTo: '', // Unassigned pending task
      salesAmount: 105000,
      remarks: 'Rush project - client needs completion by end of month for grand opening. This is a high-priority corporate client with multiple locations. Success on this project could lead to additional contracts worth over $2M. Special considerations: 1) Work must be completed outside business hours (evenings and weekends), 2) Minimal disruption to daily operations, 3) Premium materials and finishes required to match corporate brand standards, 4) All work must be completed in phases to maintain business continuity, 5) Regular progress updates required to executive team, 6) Backup plans needed for critical path items, 7) Quality control inspections at each milestone, 8) Final walkthrough with C-level executives before acceptance.'
    }
  ];

  for (const project of projects) {
    await prisma.project.create({
      data: project
    });
  }

  console.log('Database seeded successfully!');
}

main()
  .catch((e) => {
    console.error(e);
    process.exit(1);
  })
  .finally(async () => {
    await prisma.$disconnect();
  });
