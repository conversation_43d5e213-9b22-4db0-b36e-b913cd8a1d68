import 'dotenv/config';
import express from 'express';
import cors from 'cors';
import { clerkMiddleware } from '@clerk/express';
import projects from './routes/projects';
import cases from './routes/cases';
import webhooks from './routes/webhooks';
import users from './routes/users';

const app = express();

app.use(cors());
app.use(express.json());
// Enable Clerk middleware for authentication
app.use(clerkMiddleware());

app.get('/health', (_req, res) => {
  res.json({ ok: true, env: process.env.NODE_ENV || 'development' });
});

app.use('/api/projects', projects);
app.use('/api/cases', cases);
app.use('/api/webhooks', webhooks);
app.use('/api/users', users);

const port = process.env.PORT || 4000;
app.listen(port, () => {
  console.log(`API listening on http://localhost:${port}`);
});

