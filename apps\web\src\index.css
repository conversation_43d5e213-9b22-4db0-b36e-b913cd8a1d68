@tailwind base;
@tailwind components;
@tailwind utilities;

/* Definition of the design system. All colors, gradients, fonts, etc should be defined here. 
All colors MUST be HSL.
*/

@layer base {
  :root {
    --background: 0 0% 98%;
    --foreground: 222 84% 4.9%;

    --card: 0 0% 100%;
    --card-foreground: 222 84% 4.9%;

    --popover: 0 0% 100%;
    --popover-foreground: 222 84% 4.9%;

    --primary: 222 84% 4.9%;
    --primary-foreground: 210 40% 98%;

    --secondary: 220 14.3% 95.9%;
    --secondary-foreground: 222 84% 4.9%;

    --muted: 220 14.3% 95.9%;
    --muted-foreground: 220 8.9% 46.1%;

    --accent: 220 14.3% 95.9%;
    --accent-foreground: 222 84% 4.9%;

    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 210 40% 98%;

    --border: 220 13% 91%;
    --input: 220 13% 91%;
    --ring: 222 84% 4.9%;

    /* Workflow status colors */
    --status-pending: 43 96% 56%;
    --status-pending-foreground: 26 83% 14%;
    --status-in-progress: 217 91% 60%;
    --status-in-progress-foreground: 210 40% 98%;
    --status-review: 269 97% 85%;
    --status-review-foreground: 262 83% 58%;
    --status-complete: 142 76% 36%;
    --status-complete-foreground: 210 40% 98%;

    /* Role colors */
    --role-sales: 199 89% 48%;
    --role-designer: 271 81% 56%;
    --role-supervisor: 142 71% 45%;

    /* Gradients */
    --gradient-primary: linear-gradient(135deg, hsl(222 84% 4.9%), hsl(222 84% 25%));
    --gradient-card: linear-gradient(145deg, hsl(0 0% 100%), hsl(220 13% 98%));
    --gradient-status: linear-gradient(90deg, hsl(var(--status-in-progress) / 0.1), hsl(var(--status-complete) / 0.1));

    /* Shadows */
    --shadow-card: 0 2px 8px -2px hsl(222 84% 4.9% / 0.08);
    --shadow-lg: 0 10px 25px -3px hsl(222 84% 4.9% / 0.1);

    --radius: 0.5rem;

    --sidebar-background: 0 0% 98%;

    --sidebar-foreground: 240 5.3% 26.1%;

    --sidebar-primary: 240 5.9% 10%;

    --sidebar-primary-foreground: 0 0% 98%;

    --sidebar-accent: 240 4.8% 95.9%;

    --sidebar-accent-foreground: 240 5.9% 10%;

    --sidebar-border: 220 13% 91%;

    --sidebar-ring: 217.2 91.2% 59.8%;
  }

  .dark {
    --background: 222.2 84% 4.9%;
    --foreground: 210 40% 98%;

    --card: 222.2 84% 4.9%;
    --card-foreground: 210 40% 98%;

    --popover: 222.2 84% 4.9%;
    --popover-foreground: 210 40% 98%;

    --primary: 210 40% 98%;
    --primary-foreground: 222.2 47.4% 11.2%;

    --secondary: 217.2 32.6% 17.5%;
    --secondary-foreground: 210 40% 98%;

    --muted: 217.2 32.6% 17.5%;
    --muted-foreground: 215 20.2% 65.1%;

    --accent: 217.2 32.6% 17.5%;
    --accent-foreground: 210 40% 98%;

    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 210 40% 98%;

    --border: 217.2 32.6% 17.5%;
    --input: 217.2 32.6% 17.5%;
    --ring: 212.7 26.8% 83.9%;
    --sidebar-background: 240 5.9% 10%;
    --sidebar-foreground: 240 4.8% 95.9%;
    --sidebar-primary: 224.3 76.3% 48%;
    --sidebar-primary-foreground: 0 0% 100%;
    --sidebar-accent: 240 3.7% 15.9%;
    --sidebar-accent-foreground: 240 4.8% 95.9%;
    --sidebar-border: 240 3.7% 15.9%;
    --sidebar-ring: 217.2 91.2% 59.8%;
  }
}

@layer base {
  * {
    @apply border-border;
  }

  body {
    @apply bg-background text-foreground;
  }
}