import { useState } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { AlertDialog, AlertDialogTrigger, AlertDialogContent, AlertDialogHeader, AlertDialogTitle, AlertDialogDescription, AlertDialogFooter, AlertDialogCancel } from "@/components/ui/alert-dialog";
import { User } from "@/types/project";
import { User as UserIcon } from "lucide-react";

const SUPERVISOR_PHASES = [
  'floor_protection', 'plaster_ceiling', 'spc', 'first_painting',
  'carpentry_measure', 'measure_others', 'carpentry_install', 'quartz_measure', 'quartz_install',
  'glass_measure', 'glass_install', 'final_wiring', 'final_painting', 'install_others',
  'plumbing', 'cleaning', 'defects'
] as const;

export function SupervisorAssignInline({ users = [], projectId, onAssign }: { users: User[]; projectId: string; onAssign?: (projectId: string, assigneeId: string, phases?: string[]) => void; }) {
  const [open, setOpen] = useState(false);
  const [assignee, setAssignee] = useState("");
  const [selected, setSelected] = useState<string[]>([]);

  const toggle = (phase: string) => {
    setSelected((prev) => prev.includes(phase) ? prev.filter(p => p !== phase) : [...prev, phase]);
  };

  const confirm = () => {
    if (!assignee || selected.length === 0) return;
    onAssign?.(projectId, assignee, selected);
    setOpen(false);
    setSelected([]);
    setAssignee("");
  };

  return (
    <AlertDialog open={open} onOpenChange={setOpen}>
      <AlertDialogTrigger asChild>
        <Button variant="default" size="sm">
          <UserIcon className="h-4 w-4 mr-1" /> Assign / Reassign
        </Button>
      </AlertDialogTrigger>
      <AlertDialogContent>
        <AlertDialogHeader>
          <AlertDialogTitle>Assign Supervisor and Phases</AlertDialogTitle>
          <AlertDialogDescription>
            Select a supervisor and one or more phases to assign.
          </AlertDialogDescription>
        </AlertDialogHeader>
        <div className="space-y-3 py-2">
          <div>
            <label className="text-sm">Assign to</label>
            <Select value={assignee} onValueChange={setAssignee}>
              <SelectTrigger>
                <SelectValue placeholder="Select a supervisor" />
              </SelectTrigger>
              <SelectContent>
                {users.filter(u => u.role === 'supervisor').map(u => (
                  <SelectItem key={u.id} value={u.id}>{u.name}</SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>
          <div>
            <label className="text-sm">Select tasks</label>
            <div className="grid grid-cols-2 gap-2 max-h-64 overflow-auto mt-2">
              {SUPERVISOR_PHASES.map((phase) => (
                <label key={phase} className="flex items-center gap-2 text-sm">
                  <input type="checkbox" checked={selected.includes(phase)} onChange={() => toggle(phase)} />
                  <span>{phase.replace(/_/g, ' ')}</span>
                </label>
              ))}
            </div>
          </div>
        </div>
        <AlertDialogFooter>
          <AlertDialogCancel>Cancel</AlertDialogCancel>
          <Button onClick={confirm} disabled={!assignee || selected.length === 0}>Confirm</Button>
        </AlertDialogFooter>
      </AlertDialogContent>
    </AlertDialog>
  );
}

