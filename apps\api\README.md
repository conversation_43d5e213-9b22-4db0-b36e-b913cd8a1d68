API (Express + TypeScript + Prisma)

Scripts
- dev: tsx watch src/index.ts
- build: tsc -p tsconfig.json
- start: node dist/index.js
- db:migrate: prisma migrate dev
- db:generate: prisma generate

Setup
1) cp .env.example .env
2) Update DATABASE_URL to your local Postgres
3) From repo root, run: npm install
4) Generate and migrate: npm run -w @limico/api db:generate && npm run -w @limico/api db:migrate
5) Start API: npm run -w @limico/api dev

Notes
- Schema is compatible with Supabase (Postgres).
- Use PROJECT endpoints at http://localhost:4000

