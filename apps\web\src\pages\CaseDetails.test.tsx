import { render, screen } from '@testing-library/react';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import { MemoryRouter, Route, Routes } from 'react-router-dom';
import CaseDetails from './CaseDetails';

const renderWithProviders = (ui: React.ReactNode, route = '/case/abc') => {
  const client = new QueryClient({ defaultOptions: { queries: { retry: false } } });
  window.history.pushState({}, 'Test', route);
  return render(
    <QueryClientProvider client={client}>
      <MemoryRouter initialEntries={[route]}>
        <Routes>
          <Route path="/case/:caseId" element={ui as React.ReactElement} />
        </Routes>
      </MemoryRouter>
    </QueryClientProvider>
  );
};

describe('CaseDetails', () => {
  it('renders timeline header and empty state copy', async () => {
    // Mock fetch used by Api.casesById endpoints by intercepting global fetch
    const origFetch = global.fetch as typeof fetch;
    global.fetch = (async (input: RequestInfo | URL, init?: RequestInit) => {
      const url = typeof input === 'string' ? input : input.url;
      if (url.includes('/api/cases/abc') && !url.endsWith('/history')) {
        return new Response(JSON.stringify({ data: { caseId: 'abc', roles: {} } }), { status: 200 });
      }
      if (url.endsWith('/api/cases/abc/history')) {
        return new Response(JSON.stringify({ caseId: 'abc', events: [] }), { status: 200 });
      }
      return new Response('Not found', { status: 404 });
    }) as typeof fetch;

    renderWithProviders(<CaseDetails />);

    expect(await screen.findByText('Timeline')).toBeInTheDocument();
    expect(await screen.findByText('No history yet for this case.')).toBeInTheDocument();

    global.fetch = origFetch;
  });
});

