import { Project, User, UserRole, ProjectStatus, SalesWonSubStatus } from "@/types/project";
import { DesignerProjectCard } from './project/role-cards/DesignerProjectCard';
import { SalesProjectCard } from './project/role-cards/SalesProjectCard';
import { SupervisorProjectCard } from './project/role-cards/SupervisorProjectCard';


interface ProjectCardProps {
  project: Project;
  userRole: UserRole;
  currentUserId: string;
  onStatusUpdate: (projectId: string, newStatus: ProjectStatus, newSubStatus?: SalesWonSubStatus, phaseCompletedAt?: string, phaseKey?: string) => void;
  onViewDetails: (project: Project) => void;
  onRevisionRequest?: (projectId: string) => void;
  onDeleteProject?: (projectId: string) => void;
  onAssignTask?: (projectId: string, assigneeId: string, phases?: string[]) => void;
  availableUsers?: User[];
}

export const ProjectCard = ({ project, userRole, currentUserId, onStatusUpdate, onViewDetails, onRevisionRequest, onDeleteProject, onAssignTask, availableUsers = [] }: ProjectCardProps) => {
  // Choose the card to render based on the project's status (task role group)
  const status = project.status as string;
  const isDesignerTask = ['designer_pending_assign','checklist','3d','2d','furniture_list','complete'].includes(status);
  const isSupervisorTask = ['supervisor_pending_assign','inprogress','completed','floor_protection','plaster_ceiling','spc','first_painting','carpentry_measure','measure_others','carpentry_install','quartz_measure','quartz_install','glass_measure','glass_install','final_wiring','final_painting','install_others','plumbing','cleaning','defects'].includes(status);

  if (isDesignerTask) {
    return (
      <DesignerProjectCard
        project={project}
        userRole={userRole}
        currentUserId={currentUserId}
        onStatusUpdate={(id: string, status: ProjectStatus) => onStatusUpdate(id, status)}
        onViewDetails={onViewDetails}
        onRevisionRequest={onRevisionRequest}
        onAssignTask={(id, assigneeId) => onAssignTask && onAssignTask(id, assigneeId)}
        availableUsers={availableUsers}
        onDeleteProject={onDeleteProject}
      />
    );
  }

  if (!isSupervisorTask) {
    // Default to Sales for any non-designer/supervisor status
    return (
      <SalesProjectCard
        project={project}
        userRole={userRole}
        currentUserId={currentUserId}
        onStatusUpdate={(id, status, sub) => onStatusUpdate(id, status, sub)}
        onViewDetails={onViewDetails}
        onDeleteProject={onDeleteProject}
        onAssignTask={(id, assigneeId) => onAssignTask && onAssignTask(id, assigneeId)}
        availableUsers={availableUsers}
      />
    );
  }

  // Supervisor card
  return (
    <SupervisorProjectCard
      project={project}
      userRole={userRole}
      currentUserId={currentUserId}
      onStatusUpdate={(id, status, _sub, date, phase) => onStatusUpdate(id, status, undefined, date, phase)}
      onViewDetails={onViewDetails}
      onAssignTask={onAssignTask}
      onDeleteProject={onDeleteProject}
      availableUsers={availableUsers}
    />
  );
};