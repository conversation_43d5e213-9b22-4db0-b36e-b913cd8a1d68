import { SupervisorStatus, SUPERVISOR_STATUS_LABELS } from "@/types/project";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>ist, TabsTrigger } from "@/components/ui/tabs";
import { ScrollArea } from "@/components/ui/scroll-area";
import { Badge } from "@/components/ui/badge";

interface SupervisorStatusTabsProps {
  currentStatus: SupervisorStatus | 'all';
  onStatusChange: (status: SupervisorStatus | 'all') => void;
}

export const SupervisorStatusTabs = ({ currentStatus, onStatusChange }: SupervisorStatusTabsProps) => {
  // Group supervisor statuses into phases for better UX
  const phases = {
    'Early Phase': ['floor_protection', 'plaster_ceiling', 'spc', 'first_painting'] as SupervisorStatus[],
    'Mid Phase': ['carpentry_measure', 'measure_others', 'carpentry_install', 'quartz_measure', 'quartz_install'] as SupervisorStatus[],
    'Late Phase': ['glass_measure', 'glass_install', 'final_wiring', 'final_painting', 'install_others'] as SupervisorStatus[],
    'Final Phase': ['plumbing', 'cleaning', 'defects'] as SupervisorStatus[]
  };

  const getPhaseColor = (phase: string) => {
    switch (phase) {
      case 'Early Phase': return 'bg-orange-100 text-orange-700 border-orange-200';
      case 'Mid Phase': return 'bg-blue-100 text-blue-700 border-blue-200';
      case 'Late Phase': return 'bg-purple-100 text-purple-700 border-purple-200';
      case 'Final Phase': return 'bg-green-100 text-green-700 border-green-200';
      default: return 'bg-gray-100 text-gray-700 border-gray-200';
    }
  };

  return (
    <div className="space-y-4">
      <Tabs value={currentStatus} onValueChange={(value) => onStatusChange(value as SupervisorStatus | 'all')}>
        <TabsList className="inline-flex h-10 items-center justify-start rounded-md bg-muted p-1 text-muted-foreground w-auto">
          <TabsTrigger value="all">All Tasks</TabsTrigger>
        </TabsList>
        
        <ScrollArea className="w-full mt-4">
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
            {Object.entries(phases).map(([phaseName, statuses]) => (
              <div key={phaseName} className="space-y-2">
                <Badge variant="outline" className={`w-full justify-center ${getPhaseColor(phaseName)}`}>
                  {phaseName}
                </Badge>
                <div className="space-y-1">
                  {statuses.map((status) => (
                    <TabsTrigger
                      key={status}
                      value={status}
                      className="w-full text-xs h-auto py-2 px-2 whitespace-normal text-left justify-start"
                    >
                      {SUPERVISOR_STATUS_LABELS[status]}
                    </TabsTrigger>
                  ))}
                </div>
              </div>
            ))}
          </div>
        </ScrollArea>
      </Tabs>
    </div>
  );
};