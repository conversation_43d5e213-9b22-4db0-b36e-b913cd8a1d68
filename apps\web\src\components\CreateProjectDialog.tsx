import { useState } from "react";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, DialogTrigger } from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { Plus } from "lucide-react";
import { User, UserRole } from "@/types/project";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";

interface CreateProjectDialogProps {
  onCreateProject: (project: { title: string; client: string; salesAmount: number; assignedTo: string }) => void;
  currentUser: User;
  users: User[];
}

export const CreateProjectDialog = ({ onCreateProject, currentUser, users }: CreateProjectDialogProps) => {
  const [open, setOpen] = useState(false);
  const [formData, setFormData] = useState({
    title: '',
    client: '',
    salesAmount: '',
    assignedTo: ''
  });

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();

    if (!formData.title || !formData.client || !formData.salesAmount || !formData.assignedTo) {
      return; // Required fields; UI already uses required attributes
    }

    onCreateProject({
      title: formData.title,
      client: formData.client,
      assignedTo: formData.assignedTo,
      salesAmount: parseInt(formData.salesAmount, 10),
    });

    setFormData({
      title: '',
      client: '',
      salesAmount: '',
      assignedTo: ''
    });
    setOpen(false);
  };

  const handleInputChange = (field: string, value: string) => {
    setFormData(prev => ({ ...prev, [field]: value }));
  };

  return (
    <Dialog open={open} onOpenChange={setOpen}>
      <DialogTrigger asChild>
        <Button className="gap-2">
          <Plus className="h-4 w-4" />
          New Project
        </Button>
      </DialogTrigger>
      <DialogContent className="sm:max-w-[500px]">
        <DialogHeader>
          <DialogTitle>Create New Project</DialogTitle>
        </DialogHeader>
        <form onSubmit={handleSubmit} className="space-y-4">
          <div className="space-y-2">
            <Label htmlFor="title">Project Title</Label>
            <Input
              id="title"
              placeholder="Enter project title"
              value={formData.title}
              onChange={(e) => handleInputChange('title', e.target.value)}
              required
            />
          </div>
          
          <div className="space-y-2">
            <Label htmlFor="client">Client Name</Label>
            <Input
              id="client"
              placeholder="Enter client name"
              value={formData.client}
              onChange={(e) => handleInputChange('client', e.target.value)}
              required
            />
          </div>

          <div className="space-y-2">
            <Label htmlFor="salesAmount">Sales Amount ($)</Label>
            <Input
              id="salesAmount"
              type="number"
              placeholder="Enter total sales amount"
              value={formData.salesAmount}
              onChange={(e) => handleInputChange('salesAmount', e.target.value)}
              required
            />
          </div>

          {/* Assignee selection: now mandatory for all creators */}
          <div className="space-y-2">
            <Label>Assign to Sales</Label>
            <Select
              value={formData.assignedTo}
              onValueChange={(v) => handleInputChange('assignedTo', v)}
              required
            >
              <SelectTrigger>
                <SelectValue placeholder="Select sales assignee" />
              </SelectTrigger>
              <SelectContent>
                {users
                  .filter(u => u.role === 'sales')
                  .map((u) => (
                    <SelectItem key={u.id} value={u.id}>{u.name}</SelectItem>
                  ))}
              </SelectContent>
            </Select>
          </div>

          <div className="flex justify-end gap-2 pt-4">
            <Button type="button" variant="outline" onClick={() => setOpen(false)}>
              Cancel
            </Button>
            <Button type="submit">Create Project</Button>
          </div>
        </form>
      </DialogContent>
    </Dialog>
  );
};