import {
  Project,
  UserRole,
  ProjectStatus,
  SalesWonSubStatus,
  getStatusesForRole,
  canModifyTask,
  is<PERSON>anager,
  SALES_WON_SUB_LABELS,
} from "@/types/project";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { StatusBadge } from "@/components/StatusBadge";
import { statusToTone, shouldHideActionButtons } from "@/lib/status";
import { Progress } from "@/components/ui/progress";
import {
  AlertDialog,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogTrigger,
} from "@/components/ui/alert-dialog";
import { Calendar, User as UserIcon, Eye, ArrowRight, DollarSign } from "lucide-react";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";

import { formatDate, formatCurrency } from "@/lib/datetime";

interface SalesProjectCardProps {
  project: Project;
  userRole: UserRole;
  currentUserId: string;
  onStatusUpdate: (projectId: string, newStatus: ProjectStatus, newSubStatus?: SalesWonSubStatus) => void;
  onViewDetails: (project: Project) => void;
  onDeleteProject?: (projectId: string) => void;
  onAssignTask?: (projectId: string, assigneeId: string) => void;
  availableUsers?: { id: string; name: string; role: UserRole }[];
}

export const SalesProjectCard = ({
  project,
  userRole,
  currentUserId,
  onStatusUpdate,
  onViewDetails,

  onAssignTask,
  availableUsers = [],
}: SalesProjectCardProps) => {
  const isLostDeal = project.status === "lost_deal";

  // use shared StatusBadge for colors

  const getProgressPercentage = (status: ProjectStatus, sub?: SalesWonSubStatus): number => {
    // Build a linear sequence that includes won_deal sub-status milestones
    // Order: lead -> quotation -> potential -> won_deal(10%,5%,45%,37%,3%) -> lost_deal -> completed
    const steps: Array<{ status: ProjectStatus; sub?: SalesWonSubStatus }> = [
      { status: "lead" },
      { status: "quotation" },
      { status: "potential" },
      { status: "won_deal", sub: "10%" },
      { status: "won_deal", sub: "5%" },
      { status: "won_deal", sub: "45%" },
      { status: "won_deal", sub: "37%" },
      { status: "won_deal", sub: "3%" },
      { status: "lost_deal" },
      { status: "completed" },
    ];

    	// Normalize status strings to be robust against casing and spacing differences
    	const normalizedStatus = (String(status || "").trim().toLowerCase().replace(/\s+/g, "_").replace(/_+$/,"") as string);


    const findIndex = (): number => {
      // If status is won_deal but sub is missing, treat as first sub-step (10%)
      if (normalizedStatus === "won_deal" && !sub) {
        return steps.findIndex((s) => s.status === "won_deal" && s.sub === "10%");
      }
      // Handle "completed" as the final step (Sales canonical)
      if (normalizedStatus === "completed") {
        return steps.findIndex((s) => s.status === "completed");
      }
      return steps.findIndex((s) => s.status === (normalizedStatus as ProjectStatus) && (normalizedStatus !== "won_deal" || s.sub === sub));
    };

    const idx = findIndex();
    return idx >= 0 ? ((idx + 1) / steps.length) * 100 : 0;
  };

  const canModify = canModifyTask(userRole, project.assignedTo || "", currentUserId);
  const isMgr = isManager(userRole);
  const canAssign = isMgr;

  const getNextStatus = (): { status: ProjectStatus; sub?: SalesWonSubStatus } | null => {
    // Handle won_deal sub-status progression
    if (project.status === "won_deal" && project.salesSubStatus) {
      const subStatuses: SalesWonSubStatus[] = ["10%", "5%", "45%", "37%", "3%"];
      const currentSubIndex = subStatuses.indexOf(project.salesSubStatus as SalesWonSubStatus);
      if (project.salesSubStatus === "3%") {
        return { status: "completed" };
      }
      if (currentSubIndex >= 0 && currentSubIndex < subStatuses.length - 1) {
        return { status: "won_deal", sub: subStatuses[currentSubIndex + 1] };
      }
    }

    const roleStatuses = getStatusesForRole("sales");
    const currentIndex = roleStatuses.indexOf(project.status);
    if (project.status === "potential") {
      return { status: "won_deal", sub: "10%" };
    }
    if (currentIndex >= 0 && currentIndex < roleStatuses.length - 1) {
      return { status: roleStatuses[currentIndex + 1] };
    }
    return null;
  };

  const progressPercentage = getProgressPercentage(project.status, project.salesSubStatus as SalesWonSubStatus | undefined);
  const next = getNextStatus();

  const handleProgressClick = () => {
    if (!next) return;
    if (next.status === "won_deal") {
      onStatusUpdate(project.id, "won_deal", next.sub);
      return;
    }
    onStatusUpdate(project.id, next.status);
  };



  return (
    <Card className="hover:shadow-md transition-shadow flex flex-col h-full">
      <CardHeader className="pb-3">
        <div className="flex items-start justify-between">
          <CardTitle className="text-lg font-semibold line-clamp-2">{project.title}</CardTitle>
          <div className="flex flex-col items-end gap-1">
            <StatusBadge status={project.status} />
            {project.status === "won_deal" && project.salesSubStatus && (
              <Badge variant="secondary" className="text-xs">
                {SALES_WON_SUB_LABELS[project.salesSubStatus as SalesWonSubStatus]}
              </Badge>
            )}
          </div>
        </div>
      </CardHeader>

      <CardContent className="space-y-4 flex flex-col flex-1">
        <div className="space-y-2">
          <div className="flex items-center gap-2 text-sm text-muted-foreground">
            <UserIcon className="h-4 w-4" />
            <span>{project.client}</span>
          </div>

          <div className="flex items-center gap-2 text-sm text-muted-foreground">
            <Calendar className="h-4 w-4" />
            <span>Created: {formatDate(project.createdAt)}</span>
          </div>

          {project.salesAmount && (
            <div className="flex items-center gap-2 text-sm text-muted-foreground">
              <DollarSign className="h-4 w-4" />
              <span>Sales: {formatCurrency(project.salesAmount)}</span>
            </div>
          )}

          {project.remarks && (
            <div className="text-sm">
              <span className="text-muted-foreground">Remarks: </span>
              <span
                className="text-foreground line-clamp-2 max-w-full overflow-hidden text-ellipsis"
                style={{
                  display: '-webkit-box',
                  WebkitLineClamp: 2,
                  WebkitBoxOrient: 'vertical',
                  maxHeight: '2.5rem'
                }}
                title={project.remarks}
              >
                {project.remarks}
              </span>
            </div>
          )}
        </div>

        <div className="space-y-2">
          <div className="flex items-center justify-between text-sm">
            <span className="text-muted-foreground">Progress</span>
            <span className="font-medium">{Math.round(progressPercentage)}%</span>
          </div>
          <Progress value={progressPercentage} tone={statusToTone(project.status)} className="h-2" />
        </div>

        <div className="flex-1" />

        <div className="flex gap-2 pt-2 mt-auto">
          <Button variant="outline" size="sm" onClick={() => onViewDetails(project)} className="flex-1">
            <Eye className="h-4 w-4 mr-1" />
            Details
          </Button>

          {/* Assign on card only when unassigned; hide when lost deal */}
          {!shouldHideActionButtons(project.status) && canAssign && !project.assignedTo && (
            <AlertDialog>
              <AlertDialogTrigger asChild>
                <Button variant="default" size="sm" className="flex-1">
                  <UserIcon className="h-4 w-4 mr-1" />
                  Assign
                </Button>
              </AlertDialogTrigger>
              <AlertDialogContent>
                <AlertDialogHeader>
                  <AlertDialogTitle>Assign Sales Task</AlertDialogTitle>
                  <AlertDialogDescription>Select a sales to assign this task to:</AlertDialogDescription>
                </AlertDialogHeader>
                <div className="py-2">
                  <Select onValueChange={(value) => onAssignTask && onAssignTask(project.id, value)}>
                    <SelectTrigger>
                      <SelectValue placeholder="Select a sales" />
                    </SelectTrigger>
                    <SelectContent>
                      {availableUsers
                        .filter((u) => u.role === "sales")
                        .map((sales) => (
                          <SelectItem key={sales.id} value={sales.id}>
                            {sales.name}
                          </SelectItem>
                        ))}
                    </SelectContent>
                  </Select>
                </div>
                <AlertDialogFooter>
                  <AlertDialogCancel>Cancel</AlertDialogCancel>
                </AlertDialogFooter>
              </AlertDialogContent>
            </AlertDialog>
          )}

          {!shouldHideActionButtons(project.status) && next && project.assignedTo && canModify && (
            <Button size="sm" onClick={handleProgressClick} className="flex-1">
              <ArrowRight className="h-4 w-4 mr-1" />
              Progress
            </Button>
          )}
        </div>
      </CardContent>
    </Card>
  );
};

